@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VS Code Augment Plugin Downloader (Simple)
echo ========================================
echo.

:: 设置变量
set "PUBLISHER=augment"
set "EXTENSION=vscode-augment"
set "OUTPUT_DIR=%~dp0downloads"

:: 创建下载目录
if not exist "%OUTPUT_DIR%" (
    echo Creating downloads directory...
    mkdir "%OUTPUT_DIR%"
)

echo Fetching latest version from marketplace page...
echo.

:: 使用PowerShell抓取marketplace页面获取版本信息
powershell -Command "try { $url = 'https://marketplace.visualstudio.com/items?itemName=%PUBLISHER%.%EXTENSION%'; $response = Invoke-WebRequest -Uri $url -UseBasicParsing; $content = $response.Content; if ($content -match 'Version.*?(\d+\.\d+\.\d+)') { $version = $matches[1]; Write-Host 'Found version: ' $version; $version | Out-File -FilePath '%TEMP%\version.txt' -Encoding ASCII -NoNewline; exit 0 } else { Write-Host 'Could not find version information'; exit 1 } } catch { Write-Host 'Error: ' $_.Exception.Message; exit 1 }"

if %ERRORLEVEL% neq 0 (
    echo Failed to get version information!
    echo Trying with a default recent version...
    set "LATEST_VERSION=0.513.0"
    echo Using version: !LATEST_VERSION!
) else (
    set /p LATEST_VERSION=<"%TEMP%\version.txt"
)

if "%LATEST_VERSION%"=="" (
    echo No version found, using default...
    set "LATEST_VERSION=0.513.0"
)

echo.
echo Using version: %LATEST_VERSION%
echo.

:: 构建下载URL
set "DOWNLOAD_URL=https://marketplace.visualstudio.com/_apis/public/gallery/publishers/%PUBLISHER%/vsextensions/%EXTENSION%/versions/%LATEST_VERSION%/vspackage"

:: 设置输出文件名
set "OUTPUT_FILE=%OUTPUT_DIR%\%PUBLISHER%.%EXTENSION%-%LATEST_VERSION%.vsix"

echo Downloading from: %DOWNLOAD_URL%
echo Saving to: %OUTPUT_FILE%
echo.

:: 使用PowerShell下载文件
powershell -Command "try { Write-Host 'Starting download...'; $webClient = New-Object System.Net.WebClient; $webClient.Headers.Add('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'); $webClient.DownloadFile('%DOWNLOAD_URL%', '%OUTPUT_FILE%'); $fileSize = (Get-Item '%OUTPUT_FILE%').Length; Write-Host 'Download completed!'; Write-Host 'File size: ' ([math]::Round($fileSize/1MB, 2)) 'MB'; exit 0 } catch { Write-Host 'Download failed: ' $_.Exception.Message; exit 1 }"

if %ERRORLEVEL% neq 0 (
    echo Download failed! Trying alternative method...
    echo.
    
    :: 尝试使用curl (Windows 10+)
    echo Trying with curl...
    curl -L -o "%OUTPUT_FILE%" "%DOWNLOAD_URL%"
    
    if !ERRORLEVEL! neq 0 (
        echo All download methods failed!
        echo.
        echo You can try downloading manually from:
        echo %DOWNLOAD_URL%
        pause
        exit /b 1
    )
)

:: 检查文件是否存在且不为空
if not exist "%OUTPUT_FILE%" (
    echo Download failed - file not created!
    pause
    exit /b 1
)

for %%A in ("%OUTPUT_FILE%") do set "FILE_SIZE=%%~zA"
if %FILE_SIZE% lss 1000 (
    echo Download failed - file too small!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Download completed successfully!
echo ========================================
echo File location: %OUTPUT_FILE%
echo File size: %FILE_SIZE% bytes
echo.

:: 询问是否要打开下载目录
set /p OPEN_DIR="Open downloads folder? (y/n): "
if /i "%OPEN_DIR%"=="y" (
    explorer "%OUTPUT_DIR%"
)

:: 清理临时文件
if exist "%TEMP%\version.txt" del "%TEMP%\version.txt"

echo.
echo Press any key to exit...
pause >nul
