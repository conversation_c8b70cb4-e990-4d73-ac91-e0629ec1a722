# VS Code Augment Plugin Downloader
# PowerShell版本

param(
    [string]$OutputDir = "downloads"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "VS Code Augment Plugin Downloader" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置变量
$Publisher = "augment"
$Extension = "vscode-augment"
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$DownloadDir = Join-Path $ScriptDir $OutputDir

# 创建下载目录
if (!(Test-Path $DownloadDir)) {
    Write-Host "Creating downloads directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $DownloadDir -Force | Out-Null
}

Write-Host "Fetching latest version information..." -ForegroundColor Green
Write-Host ""

try {
    # 方法1: 使用VS Code Marketplace API
    $ApiUrl = "https://marketplace.visualstudio.com/_apis/public/gallery/extensionquery"
    
    $RequestBody = @{
        filters = @(
            @{
                criteria = @(
                    @{
                        filterType = 7
                        value = "$Publisher.$Extension"
                    }
                )
                pageNumber = 1
                pageSize = 1
                sortBy = 0
                sortOrder = 0
            }
        )
        assetTypes = @()
        flags = 914
    } | ConvertTo-Json -Depth 10

    $Headers = @{
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }

    Write-Host "Querying VS Code Marketplace API..." -ForegroundColor Yellow
    $Response = Invoke-RestMethod -Uri $ApiUrl -Method Post -Body $RequestBody -Headers $Headers
    
    $ExtensionInfo = $Response.results[0].extensions[0]
    $LatestVersion = $ExtensionInfo.versions[0].version
    $DisplayName = $ExtensionInfo.displayName
    $PublisherName = $ExtensionInfo.publisher.publisherName
    
    Write-Host "Plugin: $DisplayName" -ForegroundColor Green
    Write-Host "Publisher: $PublisherName" -ForegroundColor Green
    Write-Host "Latest Version: $LatestVersion" -ForegroundColor Green
    
} catch {
    Write-Host "API method failed, trying web scraping..." -ForegroundColor Yellow
    
    try {
        # 方法2: 网页抓取
        $MarketplaceUrl = "https://marketplace.visualstudio.com/items?itemName=$Publisher.$Extension"
        $WebResponse = Invoke-WebRequest -Uri $MarketplaceUrl -UseBasicParsing
        
        # 尝试从页面内容中提取版本号
        if ($WebResponse.Content -match 'Version.*?(\d+\.\d+\.\d+)') {
            $LatestVersion = $matches[1]
            Write-Host "Found version from webpage: $LatestVersion" -ForegroundColor Green
        } else {
            throw "Could not extract version from webpage"
        }
        
    } catch {
        Write-Host "All version detection methods failed. Using fallback version..." -ForegroundColor Red
        $LatestVersion = "0.513.0"
        Write-Host "Using fallback version: $LatestVersion" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Preparing download..." -ForegroundColor Green

# 构建下载URL
$DownloadUrl = "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/$Publisher/vsextensions/$Extension/versions/$LatestVersion/vspackage"
$OutputFile = Join-Path $DownloadDir "$Publisher.$Extension-$LatestVersion.vsix"

Write-Host "Download URL: $DownloadUrl" -ForegroundColor Cyan
Write-Host "Output File: $OutputFile" -ForegroundColor Cyan
Write-Host ""

try {
    Write-Host "Starting download..." -ForegroundColor Green
    
    # 使用WebClient进行下载，支持进度显示
    $WebClient = New-Object System.Net.WebClient
    $WebClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    # 注册进度事件
    Register-ObjectEvent -InputObject $WebClient -EventName DownloadProgressChanged -Action {
        $Global:DownloadProgress = $Event.SourceEventArgs.ProgressPercentage
        Write-Progress -Activity "Downloading Augment Plugin" -Status "Progress: $($Event.SourceEventArgs.ProgressPercentage)%" -PercentComplete $Event.SourceEventArgs.ProgressPercentage
    } | Out-Null
    
    # 开始下载
    $WebClient.DownloadFile($DownloadUrl, $OutputFile)
    
    # 清理事件
    $WebClient.Dispose()
    Write-Progress -Activity "Downloading Augment Plugin" -Completed
    
    # 检查文件
    if (Test-Path $OutputFile) {
        $FileSize = (Get-Item $OutputFile).Length
        $FileSizeMB = [math]::Round($FileSize / 1MB, 2)
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Download completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "File: $OutputFile" -ForegroundColor Cyan
        Write-Host "Size: $FileSizeMB MB" -ForegroundColor Cyan
        Write-Host ""
        
        # 询问是否打开文件夹
        $OpenFolder = Read-Host "Open downloads folder? (y/n)"
        if ($OpenFolder -eq 'y' -or $OpenFolder -eq 'Y') {
            Start-Process explorer.exe -ArgumentList $DownloadDir
        }
        
    } else {
        throw "Download completed but file not found"
    }
    
} catch {
    Write-Host ""
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "You can try downloading manually from:" -ForegroundColor Yellow
    Write-Host $DownloadUrl -ForegroundColor Cyan
    
    # 尝试打开浏览器
    $OpenBrowser = Read-Host "Open download URL in browser? (y/n)"
    if ($OpenBrowser -eq 'y' -or $OpenBrowser -eq 'Y') {
        Start-Process $DownloadUrl
    }
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
