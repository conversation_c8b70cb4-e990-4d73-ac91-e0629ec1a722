@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VS Code Augment Plugin Downloader
echo ========================================
echo.

:: 设置变量
set "PUBLISHER=augment"
set "EXTENSION=vscode-augment"
set "TEMP_JSON=%TEMP%\augment_info.json"
set "OUTPUT_DIR=%~dp0downloads"

:: 创建下载目录
if not exist "%OUTPUT_DIR%" (
    echo Creating downloads directory...
    mkdir "%OUTPUT_DIR%"
)

echo Fetching latest version information...
echo.

:: 获取插件信息的API URL
set "API_URL=https://marketplace.visualstudio.com/_apis/public/gallery/extensionquery"

:: 创建POST请求的JSON数据
echo {"filters":[{"criteria":[{"filterType":7,"value":"%PUBLISHER%.%EXTENSION%"}],"pageNumber":1,"pageSize":1,"sortBy":0,"sortOrder":0}],"assetTypes":[],"flags":914} > "%TEMP%\request.json"

:: 使用PowerShell发送API请求获取版本信息
powershell -Command "try { $response = Invoke-RestMethod -Uri '%API_URL%' -Method Post -ContentType 'application/json' -InFile '%TEMP%\request.json'; $extension = $response.results[0].extensions[0]; $version = $extension.versions[0].version; $displayName = $extension.displayName; Write-Host 'Plugin: ' $displayName; Write-Host 'Latest Version: ' $version; Write-Host 'Publisher: ' $extension.publisher.publisherName; $version | Out-File -FilePath '%TEMP%\version.txt' -Encoding ASCII -NoNewline; exit 0 } catch { Write-Host 'Error fetching version information: ' $_.Exception.Message; exit 1 }"

if %ERRORLEVEL% neq 0 (
    echo Failed to fetch version information!
    pause
    exit /b 1
)

:: 读取版本号
set /p LATEST_VERSION=<"%TEMP%\version.txt"

if "%LATEST_VERSION%"=="" (
    echo Failed to get version number!
    pause
    exit /b 1
)

echo.
echo Latest version found: %LATEST_VERSION%
echo.

:: 构建下载URL
set "DOWNLOAD_URL=https://marketplace.visualstudio.com/_apis/public/gallery/publishers/%PUBLISHER%/vsextensions/%EXTENSION%/versions/%LATEST_VERSION%/vspackage"

:: 设置输出文件名
set "OUTPUT_FILE=%OUTPUT_DIR%\%PUBLISHER%.%EXTENSION%-%LATEST_VERSION%.vsix"

echo Downloading from: %DOWNLOAD_URL%
echo Saving to: %OUTPUT_FILE%
echo.

:: 使用PowerShell下载文件
powershell -Command "try { $ProgressPreference = 'Continue'; Write-Host 'Starting download...'; Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%OUTPUT_FILE%' -UseBasicParsing; $fileSize = (Get-Item '%OUTPUT_FILE%').Length; Write-Host 'Download completed successfully!'; Write-Host 'File size: ' ([math]::Round($fileSize/1MB, 2)) 'MB'; exit 0 } catch { Write-Host 'Download failed: ' $_.Exception.Message; exit 1 }"

if %ERRORLEVEL% neq 0 (
    echo Download failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Download completed successfully!
echo ========================================
echo File location: %OUTPUT_FILE%
echo.

:: 询问是否要打开下载目录
set /p OPEN_DIR="Open downloads folder? (y/n): "
if /i "%OPEN_DIR%"=="y" (
    explorer "%OUTPUT_DIR%"
)

:: 清理临时文件
if exist "%TEMP%\request.json" del "%TEMP%\request.json"
if exist "%TEMP%\version.txt" del "%TEMP%\version.txt"
if exist "%TEMP_JSON%" del "%TEMP_JSON%"

echo.
echo Press any key to exit...
pause >nul
