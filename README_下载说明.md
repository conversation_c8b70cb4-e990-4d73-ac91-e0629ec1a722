# VS Code Augment 插件下载脚本

本工具包含三个脚本，用于自动下载 VS Code Augment 插件的最新版本。

## 文件说明

### 1. `download_augment.bat` (推荐)
- **功能**: 完整版批处理脚本
- **特点**: 使用 VS Code Marketplace API 获取最新版本
- **适用**: Windows 系统
- **依赖**: PowerShell (Windows 内置)

### 2. `download_augment_simple.bat` (备用)
- **功能**: 简化版批处理脚本
- **特点**: 网页抓取 + 备用版本号
- **适用**: Windows 系统
- **依赖**: PowerShell 或 curl

### 3. `download_augment.ps1` (最佳)
- **功能**: PowerShell 版本
- **特点**: 最稳定，支持进度显示
- **适用**: Windows PowerShell
- **依赖**: PowerShell 3.0+

## 使用方法

### 方法一：直接运行批处理文件
1. 双击 `download_augment.bat`
2. 等待脚本自动获取最新版本并下载
3. 下载完成后会询问是否打开下载文件夹

### 方法二：运行 PowerShell 脚本
1. 右键点击 `download_augment.ps1`
2. 选择"使用 PowerShell 运行"
3. 或者在 PowerShell 中执行：
   ```powershell
   .\download_augment.ps1
   ```

### 方法三：命令行运行
```cmd
# 运行批处理文件
download_augment.bat

# 或运行 PowerShell 脚本
powershell -ExecutionPolicy Bypass -File download_augment.ps1
```

## 下载位置

所有脚本都会在当前目录下创建 `downloads` 文件夹，下载的文件命名格式为：
```
augment.vscode-augment-[版本号].vsix
```

例如：`augment.vscode-augment-0.513.0.vsix`

## 安装插件

下载完成后，有两种安装方式：

### 方式一：VS Code 界面安装
1. 打开 VS Code
2. 按 `Ctrl+Shift+X` 打开扩展面板
3. 点击右上角的 `...` 菜单
4. 选择"从 VSIX 安装..."
5. 选择下载的 `.vsix` 文件

### 方式二：命令行安装
```bash
code --install-extension path/to/augment.vscode-augment-x.x.x.vsix
```

## 故障排除

### 1. 网络连接问题
- 确保网络连接正常
- 检查防火墙设置
- 尝试使用 VPN

### 2. PowerShell 执行策略问题
如果遇到执行策略错误，运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 3. 版本获取失败
- 脚本会自动使用备用版本号
- 可以手动修改脚本中的版本号

### 4. 下载失败
- 尝试运行简化版脚本
- 手动访问下载链接
- 检查磁盘空间

## 手动下载链接格式

如果脚本无法工作，可以手动构建下载链接：
```
https://marketplace.visualstudio.com/_apis/public/gallery/publishers/augment/vsextensions/vscode-augment/versions/[版本号]/vspackage
```

将 `[版本号]` 替换为实际版本号，例如：
```
https://marketplace.visualstudio.com/_apis/public/gallery/publishers/augment/vsextensions/vscode-augment/versions/0.513.0/vspackage
```

## 注意事项

1. **安全提醒**: 只从官方 VS Code Marketplace 下载插件
2. **版本更新**: 脚本会自动获取最新版本
3. **文件大小**: Augment 插件约 35-40MB
4. **系统要求**: 需要 Windows 10+ 和 VS Code 1.82.0+

## 技术细节

脚本使用以下技术：
- VS Code Marketplace REST API
- PowerShell Web 请求
- HTML 内容解析
- 文件下载和验证

## 许可证

本脚本仅用于从官方渠道下载插件，不涉及任何版权问题。
下载的插件版权归 Augment 公司所有。
